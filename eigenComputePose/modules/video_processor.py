"""
Video processing module using OpenPose for pose estimation.
"""
import sys
import cv2
import numpy as np
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
from tqdm import tqdm

# Set up OpenPose environment
try:
    from config import setup_openpose_environment
    setup_openpose_environment()
    from openpose import pyopenpose as op
except ImportError as e:
    logging.error(f"Failed to import OpenPose: {e}")
    op = None

from .utils import (
    validate_video_file,
    get_video_properties,
    create_output_directory,
    save_json_results
)

logger = logging.getLogger(__name__)

class VideoProcessor:
    """
    Video processor for extracting pose keypoints using OpenPose.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize video processor with configuration.

        Args:
            config: Configuration dictionary containing OpenPose and video settings
        """
        self.config = config
        self.openpose_config = config.get("openpose", {})
        self.video_config = config.get("video", {})
        self.gait_config = config.get("gait", {})

        self.op_wrapper = None
        self._initialize_openpose()

    def _initialize_openpose(self) -> None:
        """Initialize OpenPose wrapper."""
        if op is None:
            raise RuntimeError("OpenPose not available. Please check installation.")

        try:
            # Configure OpenPose parameters
            params = dict()
            params.update(self.openpose_config)

            # Initialize wrapper
            self.op_wrapper = op.WrapperPython()
            self.op_wrapper.configure(params)
            self.op_wrapper.start()

            logger.info("OpenPose initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize OpenPose: {e}")
            raise

    def process_video(self, video_path: Path, output_dir: Path, visualize: bool = False) -> Dict[str, Any]:
        """
        Process video and extract pose keypoints.

        Args:
            video_path: Path to input video
            output_dir: Directory to save results
            visualize: Whether to save visualization frames and video

        Returns:
            Dictionary containing processing results and metadata
        """
        logger.info(f"Processing video: {video_path}")

        # Validate video file
        if not validate_video_file(video_path, self.video_config.get("supported_formats", [])):
            raise ValueError(f"Invalid video file: {video_path}")

        # Get video properties
        video_props = get_video_properties(video_path)
        if not video_props:
            raise ValueError(f"Cannot read video properties: {video_path}")

        logger.info(f"Video properties: {video_props}")

        # Check video constraints
        if video_props["fps"] < self.video_config.get("fps_threshold", 24):
            logger.warning(f"Low FPS detected: {video_props['fps']}")

        if video_props["duration"] < self.video_config.get("min_duration", 2.0):
            raise ValueError(f"Video too short: {video_props['duration']}s")

        if video_props["duration"] > self.video_config.get("max_duration", 300.0):
            raise ValueError(f"Video too long: {video_props['duration']}s")

        # Process video frames
        keypoints_data = self._extract_keypoints(video_path, output_dir, visualize)

        # Prepare results
        results = {
            "video_path": str(video_path),
            "video_properties": video_props,
            "processing_config": {
                "openpose": self.openpose_config,
                "video": self.video_config,
            },
            "keypoints_data": keypoints_data,
            "total_frames": len(keypoints_data),
            "frames_with_detection": sum(1 for frame in keypoints_data if frame["people"]),
        }

        # Save results
        results_path = output_dir / "pose_keypoints.json"
        save_json_results(results, results_path)

        logger.info(f"Video processing completed. Results saved to {results_path}")
        return results

    def _extract_keypoints(self, video_path: Path, output_dir: Path, visualize: bool = False) -> List[Dict[str, Any]]:
        """
        Extract keypoints from video frames.

        Args:
            video_path: Path to video file
            output_dir: Output directory for frame-by-frame JSON files
            visualize: Whether to save visualization frames and video

        Returns:
            List of keypoint data for each frame
        """
        keypoints_data = []

        # Create subdirectory for frame JSON files
        frames_dir = output_dir / "frames"
        frames_dir.mkdir(exist_ok=True)

        # Create visualization directories if needed
        if visualize:
            viz_frames_dir = output_dir / "visualization_frames"
            viz_frames_dir.mkdir(exist_ok=True)

        # Open video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # Setup video writer for visualization
        video_writer = None
        if visualize:
            output_video_path = output_dir / "pose_visualization.mp4"
            # Try different codecs for better compatibility
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            video_writer = cv2.VideoWriter(str(output_video_path), fourcc, fps, (width, height))

            # Check if video writer was initialized successfully
            if not video_writer.isOpened():
                logger.warning("Failed to initialize video writer with XVID, trying mp4v")
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                video_writer = cv2.VideoWriter(str(output_video_path), fourcc, fps, (width, height))

                if not video_writer.isOpened():
                    logger.error("Failed to initialize video writer with any codec")
                    video_writer = None
                else:
                    logger.info(f"Video writer initialized successfully with mp4v codec")
            else:
                logger.info(f"Video writer initialized successfully with XVID codec")

        frame_idx = 0

        with tqdm(total=total_frames, desc="Processing frames") as pbar:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # Resize frame if needed
                if self.video_config.get("resize_height"):
                    height = self.video_config["resize_height"]
                    aspect_ratio = frame.shape[1] / frame.shape[0]
                    width = int(height * aspect_ratio)
                    frame = cv2.resize(frame, (width, height))

                # Process frame with OpenPose
                frame_data, viz_frame = self._process_frame(frame, frame_idx, fps, visualize)
                keypoints_data.append(frame_data)

                # Save frame data to individual JSON file
                frame_json_path = frames_dir / f"frame_{frame_idx:06d}.json"
                save_json_results(frame_data, frame_json_path)

                # Save visualization frame if requested
                if visualize and viz_frame is not None:
                    # Save individual visualization frame
                    viz_frame_path = viz_frames_dir / f"frame_{frame_idx:06d}_pose.jpg"
                    cv2.imwrite(str(viz_frame_path), viz_frame)

                    # Write to video
                    if video_writer is not None and video_writer.isOpened():
                        video_writer.write(viz_frame)
                        if frame_idx % 50 == 0:  # Log every 50th frame
                            logger.debug(f"Wrote frame {frame_idx} to video")

                frame_idx += 1
                pbar.update(1)

        cap.release()
        if video_writer is not None:
            video_writer.release()
            logger.info(f"Visualization video saved to: {output_video_path}")

        logger.info(f"Processed {frame_idx} frames")

        return keypoints_data

    def _process_frame(self, frame: np.ndarray, frame_idx: int, fps: float, visualize: bool = False) -> Tuple[Dict[str, Any], Optional[np.ndarray]]:
        """
        Process single frame with OpenPose.

        Args:
            frame: Input frame
            frame_idx: Frame index
            fps: Video FPS
            visualize: Whether to return visualization frame

        Returns:
            Tuple of (frame data with keypoints, visualization frame if requested)
        """
        try:
            # Create OpenPose datum
            datum = op.Datum()
            datum.cvInputData = frame

            # Process frame
            self.op_wrapper.emplaceAndPop(op.VectorDatum([datum]))

            # Extract keypoints
            people_data = []
            if datum.poseKeypoints is not None and len(datum.poseKeypoints.shape) >= 2:
                for person_idx in range(datum.poseKeypoints.shape[0]):
                    person_keypoints = datum.poseKeypoints[person_idx]

                    # Convert to list format for JSON serialization
                    keypoints_list = []
                    for kp_idx in range(person_keypoints.shape[0]):
                        x, y, confidence = person_keypoints[kp_idx]
                        keypoints_list.extend([float(x), float(y), float(confidence)])

                    people_data.append({
                        "person_id": person_idx,
                        "pose_keypoints_2d": keypoints_list,
                        "face_keypoints_2d": [],  # Not used for gait analysis
                        "hand_left_keypoints_2d": [],  # Not used for gait analysis
                        "hand_right_keypoints_2d": [],  # Not used for gait analysis
                    })

            frame_data = {
                "version": 1.3,
                "frame_idx": frame_idx,
                "timestamp": frame_idx / fps if fps > 0 else 0,
                "people": people_data,
            }

            # Get visualization frame if requested
            viz_frame = None
            if visualize and datum.cvOutputData is not None:
                viz_frame = datum.cvOutputData.copy()

            return frame_data, viz_frame

        except Exception as e:
            logger.error(f"Error processing frame {frame_idx}: {e}")
            frame_data = {
                "version": 1.3,
                "frame_idx": frame_idx,
                "timestamp": frame_idx / fps if fps > 0 else 0,
                "people": [],
            }
            return frame_data, None

    def get_primary_person_keypoints(self, keypoints_data: List[Dict[str, Any]]) -> List[np.ndarray]:
        """
        Extract keypoints for the primary person (most consistently detected) across frames.

        Args:
            keypoints_data: List of frame data with keypoints

        Returns:
            List of keypoint arrays for primary person
        """
        primary_keypoints = []

        for frame_data in keypoints_data:
            people = frame_data.get("people", [])

            if not people:
                # No detection in this frame
                primary_keypoints.append(np.full((25, 3), np.nan))
                continue

            # For now, take the first person (could be improved with tracking)
            person_data = people[0]
            keypoints_flat = person_data.get("pose_keypoints_2d", [])

            if len(keypoints_flat) >= 75:  # 25 keypoints * 3 values each
                # Reshape to (25, 3) format
                keypoints = np.array(keypoints_flat).reshape(25, 3)
                primary_keypoints.append(keypoints)
            else:
                # Invalid keypoints
                primary_keypoints.append(np.full((25, 3), np.nan))

        return primary_keypoints

    def cleanup(self) -> None:
        """Cleanup OpenPose resources."""
        if self.op_wrapper:
            try:
                self.op_wrapper.stop()
                logger.info("OpenPose wrapper stopped")
            except Exception as e:
                logger.error(f"Error stopping OpenPose wrapper: {e}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        self.cleanup()
