#!/bin/bash
# Setup script for eigenPose environment
# Source this script to set up the necessary environment variables

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EIGENPOSE_ROOT="$(dirname "$SCRIPT_DIR")"
OPENPOSE_ROOT="$EIGENPOSE_ROOT/openpose"

# Check if OpenPose exists
if [ ! -d "$OPENPOSE_ROOT" ]; then
    echo "❌ OpenPose directory not found at: $OPENPOSE_ROOT"
    echo "Please ensure OpenPose is properly installed."
    return 1
fi

# Check if OpenPose library exists
OPENPOSE_LIB_PATH="$OPENPOSE_ROOT/build/src/openpose"
if [ ! -f "$OPENPOSE_LIB_PATH/libopenpose.so.1.7.0" ]; then
    echo "❌ OpenPose library not found at: $OPENPOSE_LIB_PATH/libopenpose.so.1.7.0"
    echo "Please ensure OpenPose is properly built."
    return 1
fi

# Check if Caffe library exists
CAFFE_LIB_PATH="$OPENPOSE_ROOT/build/caffe/lib"
if [ ! -f "$CAFFE_LIB_PATH/libcaffe.so.1.0.0" ]; then
    echo "❌ Caffe library not found at: $CAFFE_LIB_PATH/libcaffe.so.1.0.0"
    echo "Please ensure OpenPose is properly built with Caffe."
    return 1
fi

# Set up environment variables
export OPENPOSE_ROOT="$OPENPOSE_ROOT"
export OPENPOSE_PYTHON_PATH="$OPENPOSE_ROOT/build/python"
export OPENPOSE_MODELS_PATH="$OPENPOSE_ROOT/models"
export OPENPOSE_LIB_PATH="$OPENPOSE_LIB_PATH"
export CAFFE_LIB_PATH="$CAFFE_LIB_PATH"

# Add OpenPose library to LD_LIBRARY_PATH
if [[ ":$LD_LIBRARY_PATH:" != *":$OPENPOSE_LIB_PATH:"* ]]; then
    export LD_LIBRARY_PATH="$OPENPOSE_LIB_PATH:$LD_LIBRARY_PATH"
fi

# Add Caffe library to LD_LIBRARY_PATH
if [[ ":$LD_LIBRARY_PATH:" != *":$CAFFE_LIB_PATH:"* ]]; then
    export LD_LIBRARY_PATH="$CAFFE_LIB_PATH:$LD_LIBRARY_PATH"
fi

# Add OpenPose Python path to PYTHONPATH
if [[ ":$PYTHONPATH:" != *":$OPENPOSE_PYTHON_PATH:"* ]]; then
    export PYTHONPATH="$OPENPOSE_PYTHON_PATH:$PYTHONPATH"
fi

echo "✅ OpenPose environment set up successfully!"
echo "   OpenPose Root: $OPENPOSE_ROOT"
echo "   OpenPose Library Path: $OPENPOSE_LIB_PATH"
echo "   Caffe Library Path: $CAFFE_LIB_PATH"
echo "   Python Path: $OPENPOSE_PYTHON_PATH"
echo "   Models Path: $OPENPOSE_MODELS_PATH"
echo ""
echo "Environment variables set:"
echo "   LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "   PYTHONPATH: $PYTHONPATH"
echo ""
echo "You can now run the test script:"
echo "   python3 test_setup.py"
