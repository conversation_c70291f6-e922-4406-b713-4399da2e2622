2025-05-25 11:07:17 | INFO | __main__:main:290 | === Video Gait Analysis Platform ===
2025-05-25 11:07:17 | INFO | __main__:main:291 | OpenPose path: /home/<USER>/eigenPose/openpose
2025-05-25 11:07:17 | INFO | __main__:main:292 | Input directory: /home/<USER>/eigenPose/eigenComputePose/inputVideos
2025-05-25 11:07:17 | INFO | __main__:main:293 | Output directory: /home/<USER>/eigenPose/eigenComputePose/outputResults
2025-05-25 11:07:17 | INFO | __main__:process_single_video:56 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 11:07:17 | INFO | __main__:process_single_video:71 | Step 1: Processing video with OpenPose...
2025-05-25 11:07:32 | INFO | __main__:process_single_video:85 | Step 2: Detecting gait cycles...
2025-05-25 11:07:32 | INFO | __main__:process_single_video:92 | Step 3: Calculating biomechanical measurements...
2025-05-25 11:07:32 | INFO | __main__:process_single_video:135 | Video analysis completed in 15.20 seconds
2025-05-25 11:07:32 | INFO | __main__:process_single_video:136 | Results saved to: /home/<USER>/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 11:07:33 | INFO | __main__:main:312 | Video processing completed successfully
2025-05-25 11:16:26 | INFO | __main__:main:321 | === Video Gait Analysis Platform ===
2025-05-25 11:16:26 | INFO | __main__:main:322 | OpenPose path: /home/<USER>/eigenPose/openpose
2025-05-25 11:16:26 | INFO | __main__:main:323 | Input directory: /home/<USER>/eigenPose/eigenComputePose/inputVideos
2025-05-25 11:16:26 | INFO | __main__:main:324 | Output directory: /home/<USER>/eigenPose/eigenComputePose/outputResults
2025-05-25 11:16:26 | INFO | __main__:process_single_video:86 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 11:16:26 | INFO | __main__:process_single_video:101 | Step 1: Processing video with OpenPose...
2025-05-25 11:16:41 | INFO | __main__:process_single_video:115 | Step 2: Detecting gait cycles...
2025-05-25 11:16:41 | INFO | __main__:process_single_video:122 | Step 3: Calculating biomechanical measurements...
2025-05-25 11:16:41 | INFO | __main__:process_single_video:165 | Video analysis completed in 15.39 seconds
2025-05-25 11:16:41 | INFO | __main__:process_single_video:166 | Results saved to: /home/<USER>/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 11:16:42 | INFO | __main__:main:343 | Video processing completed successfully
2025-05-25 11:24:49 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 11:24:49 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenPose/openpose
2025-05-25 11:24:49 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenPose/eigenComputePose/inputVideos
2025-05-25 11:24:49 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenPose/eigenComputePose/outputResults
2025-05-25 11:24:49 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 11:24:49 | INFO | __main__:process_single_video:102 | Step 1: Processing video with OpenPose...
2025-05-25 11:25:05 | INFO | __main__:process_single_video:116 | Step 2: Detecting gait cycles...
2025-05-25 11:25:05 | INFO | __main__:process_single_video:123 | Step 3: Calculating biomechanical measurements...
2025-05-25 11:25:05 | INFO | __main__:process_single_video:131 | Step 4: Generating visualizations...
2025-05-25 11:25:05 | INFO | __main__:process_single_video:177 | Video analysis completed in 16.30 seconds
2025-05-25 11:25:05 | INFO | __main__:process_single_video:178 | Results saved to: /home/<USER>/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 11:25:06 | INFO | __main__:main:361 | Video processing completed successfully
2025-05-25 11:26:00 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 11:26:00 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenPose/openpose
2025-05-25 11:26:00 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenPose/eigenComputePose/inputVideos
2025-05-25 11:26:00 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenPose/eigenComputePose/outputResults
2025-05-25 11:26:00 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 11:26:00 | INFO | __main__:process_single_video:102 | Step 1: Processing video with OpenPose...
2025-05-25 11:26:16 | INFO | __main__:process_single_video:116 | Step 2: Detecting gait cycles...
2025-05-25 11:26:16 | INFO | __main__:process_single_video:123 | Step 3: Calculating biomechanical measurements...
2025-05-25 11:26:16 | INFO | __main__:process_single_video:131 | Step 4: Generating visualizations...
2025-05-25 11:26:16 | INFO | __main__:process_single_video:177 | Video analysis completed in 16.22 seconds
2025-05-25 11:26:16 | INFO | __main__:process_single_video:178 | Results saved to: /home/<USER>/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 11:26:16 | INFO | __main__:main:361 | Video processing completed successfully
2025-05-25 11:27:03 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 11:27:03 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenPose/openpose
2025-05-25 11:27:03 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenPose/eigenComputePose/inputVideos
2025-05-25 11:27:03 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenPose/eigenComputePose/outputResults
2025-05-25 11:27:04 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 11:27:04 | INFO | __main__:process_single_video:102 | Step 1: Processing video with OpenPose...
2025-05-25 11:27:20 | INFO | __main__:process_single_video:116 | Step 2: Detecting gait cycles...
2025-05-25 11:27:20 | INFO | __main__:process_single_video:123 | Step 3: Calculating biomechanical measurements...
2025-05-25 11:27:20 | INFO | __main__:process_single_video:131 | Step 4: Generating visualizations...
2025-05-25 11:27:20 | INFO | __main__:process_single_video:177 | Video analysis completed in 16.35 seconds
2025-05-25 11:27:20 | INFO | __main__:process_single_video:178 | Results saved to: /home/<USER>/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 11:27:20 | INFO | __main__:main:361 | Video processing completed successfully
2025-05-25 11:28:05 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 11:28:05 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenPose/openpose
2025-05-25 11:28:05 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenPose/eigenComputePose/inputVideos
2025-05-25 11:28:05 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenPose/eigenComputePose/outputResults
2025-05-25 11:28:05 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 11:28:05 | INFO | __main__:process_single_video:102 | Step 1: Processing video with OpenPose...
2025-05-25 11:28:21 | INFO | __main__:process_single_video:116 | Step 2: Detecting gait cycles...
2025-05-25 11:28:21 | INFO | __main__:process_single_video:123 | Step 3: Calculating biomechanical measurements...
2025-05-25 11:28:21 | INFO | __main__:process_single_video:131 | Step 4: Generating visualizations...
2025-05-25 11:28:23 | INFO | __main__:process_single_video:177 | Video analysis completed in 18.31 seconds
2025-05-25 11:28:23 | INFO | __main__:process_single_video:178 | Results saved to: /home/<USER>/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 11:28:24 | INFO | __main__:main:361 | Video processing completed successfully
2025-05-25 12:04:38 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:04:38 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:04:38 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:04:38 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:04:38 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:04:38 | ERROR | __main__:process_single_video:183 | Error processing video inputVideos/IMG_7888.mp4: OpenPose not available. Please check installation.
2025-05-25 12:04:38 | ERROR | __main__:main:358 | Video processing failed
2025-05-25 12:05:23 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:05:23 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:05:23 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:05:23 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:05:23 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:05:23 | ERROR | __main__:process_single_video:183 | Error processing video inputVideos/IMG_7888.mp4: OpenPose not available. Please check installation.
2025-05-25 12:05:23 | ERROR | __main__:main:358 | Video processing failed
2025-05-25 12:06:53 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:06:53 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:06:53 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:06:53 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:06:53 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:06:53 | ERROR | __main__:process_single_video:183 | Error processing video inputVideos/IMG_7888.mp4: OpenPose not available. Please check installation.
2025-05-25 12:06:53 | ERROR | __main__:main:358 | Video processing failed
2025-05-25 12:16:12 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:16:12 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:16:12 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:16:12 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:16:12 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:16:12 | ERROR | __main__:process_single_video:183 | Error processing video inputVideos/IMG_7888.mp4: OpenPose not available. Please check installation.
2025-05-25 12:16:12 | ERROR | __main__:main:358 | Video processing failed
2025-05-25 12:16:44 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:16:44 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:16:44 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:16:44 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:16:44 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:16:44 | ERROR | __main__:process_single_video:183 | Error processing video inputVideos/IMG_7888.mp4: OpenPose not available. Please check installation.
2025-05-25 12:16:44 | ERROR | __main__:main:358 | Video processing failed
2025-05-25 12:16:57 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:16:57 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:16:57 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:16:57 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:16:57 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:16:57 | ERROR | __main__:process_single_video:183 | Error processing video inputVideos/IMG_7888.mp4: OpenPose not available. Please check installation.
2025-05-25 12:16:57 | ERROR | __main__:main:358 | Video processing failed
2025-05-25 12:17:10 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 12:17:10 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 12:17:10 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 12:17:10 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 12:17:10 | INFO | __main__:process_single_video:87 | Starting analysis of video: inputVideos/IMG_7888.mp4
2025-05-25 12:17:11 | INFO | __main__:process_single_video:102 | Step 1: Processing video with OpenPose...
2025-05-25 12:17:27 | INFO | __main__:process_single_video:116 | Step 2: Detecting gait cycles...
2025-05-25 12:17:27 | INFO | __main__:process_single_video:123 | Step 3: Calculating biomechanical measurements...
2025-05-25 12:17:27 | INFO | __main__:process_single_video:131 | Step 4: Generating visualizations...
2025-05-25 12:17:29 | INFO | __main__:process_single_video:177 | Video analysis completed in 18.29 seconds
2025-05-25 12:17:29 | INFO | __main__:process_single_video:178 | Results saved to: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults/IMG_7888_analysis
2025-05-25 12:17:29 | INFO | __main__:main:361 | Video processing completed successfully
2025-05-25 20:18:45 | INFO | __main__:main:339 | === Video Gait Analysis Platform ===
2025-05-25 20:18:45 | INFO | __main__:main:340 | OpenPose path: /home/<USER>/eigenCompute/eigenPose/openpose
2025-05-25 20:18:45 | INFO | __main__:main:341 | Input directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/inputVideos
2025-05-25 20:18:45 | INFO | __main__:main:342 | Output directory: /home/<USER>/eigenCompute/eigenPose/eigenComputePose/outputResults
2025-05-25 20:18:45 | ERROR | __main__:main:378 | Please specify either --video or --batch
