#ifndef OPENPOSE_CORE_HEADERS_HPP
#define OPENPOSE_CORE_HEADERS_HPP

// core module
#include <openpose/core/array.hpp>
#include <openpose/core/arrayCpuGpu.hpp>
#include <openpose/core/common.hpp>
#include <openpose/core/cvMatToOpInput.hpp>
#include <openpose/core/cvMatToOpOutput.hpp>
#include <openpose/core/datum.hpp>
#include <openpose/core/enumClasses.hpp>
#include <openpose/core/gpuRenderer.hpp>
#include <openpose/core/keepTopNPeople.hpp>
#include <openpose/core/keypointScaler.hpp>
#include <openpose/core/macros.hpp>
#include <openpose/core/matrix.hpp>
#include <openpose/core/opOutputToCvMat.hpp>
#include <openpose/core/point.hpp>
#include <openpose/core/rectangle.hpp>
#include <openpose/core/renderer.hpp>
#include <openpose/core/scaleAndSizeExtractor.hpp>
#include <openpose/core/string.hpp>
#include <openpose/core/verbosePrinter.hpp>
#include <openpose/core/wCvMatToOpInput.hpp>
#include <openpose/core/wCvMatToOpOutput.hpp>
#include <openpose/core/wKeepTopNPeople.hpp>
#include <openpose/core/wKeypointScaler.hpp>
#include <openpose/core/wOpOutputToCvMat.hpp>
#include <openpose/core/wScaleAndSizeExtractor.hpp>
#include <openpose/core/wVerbosePrinter.hpp>

#endif // OPENPOSE_CORE_HEADERS_HPP
