#ifndef OPENPOSE_HAND_HEADERS_HPP
#define OPENPOSE_HAND_HEADERS_HPP

// hand module
#include <openpose/hand/handDetector.hpp>
#include <openpose/hand/handDetectorFromTxt.hpp>
#include <openpose/hand/handExtractorCaffe.hpp>
#include <openpose/hand/handExtractorNet.hpp>
#include <openpose/hand/handParameters.hpp>
#include <openpose/hand/handCpuRenderer.hpp>
#include <openpose/hand/handGpuRenderer.hpp>
#include <openpose/hand/handRenderer.hpp>
#include <openpose/hand/renderHand.hpp>
#include <openpose/hand/wHandDetector.hpp>
#include <openpose/hand/wHandDetectorFromTxt.hpp>
#include <openpose/hand/wHandDetectorTracking.hpp>
#include <openpose/hand/wHandDetectorUpdate.hpp>
#include <openpose/hand/wHandExtractorNet.hpp>
#include <openpose/hand/wHandRenderer.hpp>

#endif // OPENPOSE_HAND_HEADERS_HPP
