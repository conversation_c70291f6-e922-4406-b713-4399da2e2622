#ifndef OPENPOSE_PRODUCER_HEADERS_HPP
#define OPENPOSE_PRODUCER_HEADERS_HPP

// producer module
#include <openpose/producer/datumProducer.hpp>
#include <openpose/producer/enumClasses.hpp>
#include <openpose/producer/flirReader.hpp>
#include <openpose/producer/imageDirectoryReader.hpp>
#include <openpose/producer/ipCameraReader.hpp>
#include <openpose/producer/producer.hpp>
#include <openpose/producer/spinnakerWrapper.hpp>
#include <openpose/producer/videoCaptureReader.hpp>
#include <openpose/producer/videoReader.hpp>
#include <openpose/producer/webcamReader.hpp>
#include <openpose/producer/wDatumProducer.hpp>

#endif // OPENPOSE_PRODUCER_HEADERS_HPP
