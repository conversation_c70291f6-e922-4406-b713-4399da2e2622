#ifndef OPENPOSE_POSE_HEADERS_HPP
#define OPENPOSE_POSE_HEADERS_HPP

// pose module
#include <openpose/pose/enumClasses.hpp>
#include <openpose/pose/poseCpuRenderer.hpp>
#include <openpose/pose/poseExtractor.hpp>
#include <openpose/pose/poseExtractorCaffe.hpp>
#include <openpose/pose/poseExtractorNet.hpp>
#include <openpose/pose/poseGpuRenderer.hpp>
#include <openpose/pose/poseParameters.hpp>
#include <openpose/pose/poseParametersRender.hpp>
#include <openpose/pose/poseRenderer.hpp>
#include <openpose/pose/renderPose.hpp>
#include <openpose/pose/wPoseExtractor.hpp>
#include <openpose/pose/wPoseExtractorNet.hpp>
#include <openpose/pose/wPoseRenderer.hpp>

#endif // OPENPOSE_POSE_HEADERS_HPP
