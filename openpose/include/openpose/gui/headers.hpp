#ifndef OPENPOSE_GUI_HEADERS_HPP
#define OPENPOSE_GUI_HEADERS_HPP

// gui module
#include <openpose/gui/enumClasses.hpp>
#include <openpose/gui/frameDisplayer.hpp>
#include <openpose/gui/gui.hpp>
#include <openpose/gui/guiAdam.hpp>
#include <openpose/gui/gui3D.hpp>
#include <openpose/gui/guiInfoAdder.hpp>
#include <openpose/gui/wGui.hpp>
#include <openpose/gui/wGuiAdam.hpp>
#include <openpose/gui/wGui3D.hpp>
#include <openpose/gui/wGuiInfoAdder.hpp>

#endif // OPENPOSE_GUI_HEADERS_HPP
