#ifndef OPENPOSE_THREAD_HEADERS_HPP
#define OPENPOSE_THREAD_HEADERS_HPP

// thread module
#include <openpose/thread/enumClasses.hpp>
#include <openpose/thread/priorityQueue.hpp>
#include <openpose/thread/queue.hpp>
#include <openpose/thread/queueBase.hpp>
#include <openpose/thread/subThread.hpp>
#include <openpose/thread/subThreadNoQueue.hpp>
#include <openpose/thread/subThreadQueueIn.hpp>
#include <openpose/thread/subThreadQueueInOut.hpp>
#include <openpose/thread/subThreadQueueOut.hpp>
#include <openpose/thread/thread.hpp>
#include <openpose/thread/threadManager.hpp>
#include <openpose/thread/worker.hpp>
#include <openpose/thread/workerProducer.hpp>
#include <openpose/thread/workerConsumer.hpp>
#include <openpose/thread/wFpsMax.hpp>
#include <openpose/thread/wIdGenerator.hpp>
#include <openpose/thread/wQueueAssembler.hpp>
#include <openpose/thread/wQueueOrderer.hpp>

#endif // OPENPOSE_THREAD_HEADERS_HPP
