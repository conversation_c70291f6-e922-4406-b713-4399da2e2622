OpenPose Doc - Major Released Features
====================================

- Nov 2020: [**Python API improved and included on Windows portable binaries**](https://github.com/CMU-Perceptual-Computing-Lab/openpose/releases)!
- Nov 2020: [Compatibility with Nvidia 30XX cards, CUDA 11, and Ubuntu 20](installation/0_index.md)!
- Sep 2019: [**Training code released**](https://github.com/CMU-Perceptual-Computing-Lab/openpose_train)!
- Jan 2019: [**Unity plugin released**](https://github.com/CMU-Perceptual-Computing-Lab/openpose_unity_plugin)!
- Jan 2019: [**Improved Python API**](doc/03_python_api.md) released! Including body, face, hands, and all the functionality of the C++ API!
- Dec 2018: [**Foot dataset released**](https://cmu-perceptual-computing-lab.github.io/foot_keypoint_dataset) and [**new paper released**](https://arxiv.org/abs/1812.08008)!
- Sep 2018: [**Experimental tracker**](01_demo.md#tracking)!
- Jun 2018: [**Combined body-foot model released! 40% faster and 5% more accurate**](installation/0_index.md)!
- Jun 2018: [**Python API**](03_python_api.md) released!
- Jun 2018: [**OpenCL/AMD graphic card version**](installation/0_index.md) released!
- Jun 2018: [**Calibration toolbox**](advanced/calibration_module.md) released!
- Jun 2018: [**Mac OSX version (CPU)**](installation/0_index.md) released!
- Mar 2018: [**CPU version**](installation/0_index.md)!
- Mar 2018: [**3-D keypoint reconstruction module**](advanced/3d_reconstruction_module.md) (from multiple camera views)!
- Sep 2017: [**CMake**](installation/0_index.md) installer and **IP camera** support!
- Jul 2017: [**Windows portable binaries and demo**](https://github.com/CMU-Perceptual-Computing-Lab/openpose/releases)!
- Jul 2017: **Hands** released!
- Jun 2017: **Face** released!
- May 2017: **Windows** version!
- Apr 2017: **Body** released!
For further details, check the [release notes](08_release_notes.md).
