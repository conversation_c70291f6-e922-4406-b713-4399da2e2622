OpenPose Doc - Authors and Contributors
====================================



## Authors
OpenPose is authored by [<PERSON><PERSON><PERSON>](https://www.gineshidalgo.com), [<PERSON><PERSON>](https://people.eecs.berkeley.edu/~zhecao), [<PERSON>](http://www.cs.cmu.edu/~tsimon), [<PERSON><PERSON><PERSON><PERSON>](https://scholar.google.com/citations?user=sFQD3k4AAAAJ&hl=en), [Ya<PERSON>ha<PERSON>](https://www.raaj.tech), [<PERSON><PERSON><PERSON>](https://jhugestar.github.io), and [<PERSON><PERSON>](http://www.cs.cmu.edu/~yaser). It is maintained by [<PERSON><PERSON><PERSON>](https://www.gineshidalgo.com) and [Yaadhav <PERSON>aj](https://www.raaj.tech).

OpenPose would not be possible without the [**CMU Panoptic Studio dataset**](http://domedb.perception.cs.cmu.edu). The body pose estimation work is based on the following and original 2 repositories: [CVPR 2017 repository](https://github.com/ZheC/Multi-Person-Pose-Estimation) and [ECCV 2016 repository](https://github.com/CMU-Perceptual-Computing-Lab/caffe_rtpose).



## Contributors
We would also like to thank the following people, who have contributed to key components of OpenPose:
1. [Bikramjot Hanzra](https://www.linkedin.com/in/bikz05): Former OpenPose maintainer, CMake (Ubuntu and Windows) version, and initial Travis Build version for Ubuntu.
2. [Donglai Xiang](https://xiangdonglai.github.io): Camera calibration toolbox improvement, including the implementation of its bundle adjustment algorithm.
3. [Luis Fernando Fraga](https://github.com/fragalfernando): Implementation of Lukas-Kanade algorithm and person ID extractor.
4. [Akash Patwal](https://www.linkedin.com/in/akash-patwal-63a12012a): Speedup of the CUDA image resize and visual skeleton rendering, as well as extension that allows OpenPose to speedup linearly to more than 4 GPUs.
5. [Helen Medina](https://github.com/helen-medina): First Windows version.
6. [Matthijs van der Burgh](https://github.com/MatthijsBurgh): First GitHub Actions CI version for Ubuntu and Mac, and ported all the deprecated Travis CI tests into the new CI system.

We would also like to thank all the people who [have helped OpenPose in any way](https://github.com/CMU-Perceptual-Computing-Lab/openpose/graphs/contributors).
