OpenPose Doc - Community-based Projects
====================================

Here we expose all projects created with OpenPose by the community and that were shared with us. Do you want to share yours? Simply create a pull request and add to this file your demo and a description of it!

1. [**ROS OpenPose**](https://github.com/ravijo/ros_openpose): ROS wrapper for OpenPose
2. [**Hand gesture classification application - OpenHand**](https://github.com/ArthurFDLR/OpenHand-App): Third-party application that eases hand keypoints datasets creation and real-time hand gesture classification. You can deploy your own Neural Network classification model on top of OpenPose and play with it in real-time through a GUI!
3. Integrated to [Huggingface Spaces](https://huggingface.co/spaces) with [Gradio](https://github.com/gradio-app/gradio). See demo: [![Hugging Face Spaces](https://img.shields.io/badge/%F0%9F%A4%97%20Hugging%20Face-Spaces-blue)](https://huggingface.co/spaces/akhaliq/openpose)
4. [**RealSense2OpenPose3D**](https://github.com/foxtierney/RealSense2OpenPose3D): Use an Intel RealSense RGB-D camera to add depth to OpenPose. Generates JSON files with 3D keypoints.


Disclaimer: We do not support any of these projects, we are simply exposing them. GitHub issues or questions about those will result in strict user bans and the posts being deleted.
