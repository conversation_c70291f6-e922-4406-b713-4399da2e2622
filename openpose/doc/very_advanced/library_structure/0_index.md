OpenPose Very Advanced Doc - Library Structure
====================================

As a user, you do not need to know anything about this section! This section is intended for OpenPose internal developers. It is exposed publicly, but you can skip this whole folder if you are just trying to use OpenPose or create new code/demos using OpenPose.

Even if you want to e.g., change internal functions and/or extend the OpenPose functionality, the easiest solution as a user is to follow the [OpenPose C++ API](../../doc/04_cpp_api.md). If the new functionality is cool, make a pull request so we can add it to OpenPose!

In order to learn the basics about how OpenPose works internally:
1. See the Doxygen documentation on [http://cmu-perceptual-computing-lab.github.io/openpose](http://cmu-perceptual-computing-lab.github.io/openpose) or build that Doxygen doc from the source code.
2. Take a look at the [library Quick Start section](../../README.md#quick-start-overview) from the main README (or its Doxygen analog).
3. OpenPose Overview: Learn the basics about the library source code in [doc/very_advanced/library_structure/1_library_deep_overview.md](1_library_deep_overview.md).
4. Extending Functionality: Learn how to extend the library in [doc/very_advanced/library_structure/2_library_extend_functionality.md](2_library_extend_functionality.md).
5. Adding An Extra Module: Learn how to add an extra module in [doc/very_advanced/library_structure/3_library_add_new_module.md](3_library_add_new_module.md).
