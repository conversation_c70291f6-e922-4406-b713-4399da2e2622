# Deprecated Examples
You are most probably looking for the [examples/tutorial_api_cpp/](../tutorial_api_cpp/) or [examples/tutorial_api_python/](../tutorial_api_python/), which provide examples of the thread API already applied to body pose estimation.

This directory is for OpenPose deployers, not for OpenPose uses, as it only provides (potentially not updated) examples for:
- The basic OpenPose thread mechanism API, and it is only meant for people interested in the multi-thread architecture without been interested in the OpenPose pose estimation algorithm.
- How to add a new module to OpenPose. And this is already and better explained in [examples/tutorial_api_cpp/](../tutorial_api_cpp/) or [examples/tutorial_api_python/](../tutorial_api_python/).
