{"sources": [{"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/CMakeFiles/openpose_lib"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/CMakeFiles/openpose_lib.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/CMakeFiles/openpose_lib-complete.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-build.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-configure.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-download.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-install.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-mkdir.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-patch.rule"}, {"file": "/home/<USER>/eigenCompute/eigenPose/openpose/build/caffe/src/openpose_lib-stamp/openpose_lib-update.rule"}], "target": {"labels": ["openpose_lib"], "name": "openpose_lib"}}